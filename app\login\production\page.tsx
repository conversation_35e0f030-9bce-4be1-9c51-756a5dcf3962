"use client"

import { useState } from "react"
import Link from "next/link"
import { useRout<PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm, type SubmitHandler } from "react-hook-form"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"

const formSchema = z.object({
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  password: z.string().min(8, {
    message: "Password must be at least 8 characters.",
  }),
  rememberMe: z.boolean().default(false),
})

type FormData = z.infer<typeof formSchema>

export default function DashboardLoginPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  })

  const onSubmit: SubmitHandler<FormData> = async (values) => {
    setIsLoading(true)

    try {
      const { createClient } = await import("@/lib/supabase/client")
      const supabase = createClient()

      // Authenticate with Supabase
      const { error } = await supabase.auth.signInWithPassword({
        email: values.email,
        password: values.password,
      })

      if (error) {
        alert(error.message)
        setIsLoading(false)
        return
      }

      // Check user role in our database
      const response = await fetch('/api/auth/me')
      if (!response.ok) {
        await supabase.auth.signOut()
        alert('Failed to verify user account. Please try again.')
        setIsLoading(false)
        return
      }

      const userData = await response.json()

      // Validate that user is a student
      if (userData.role !== 'STUDENT') {
        await supabase.auth.signOut()
        alert('This login is for students only. Please use the admin login if you are an administrator.')
        setIsLoading(false)
        return
      }

      // Check KYC status for production dashboard access
      const profileResponse = await fetch('/api/student/profile')
      if (!profileResponse.ok) {
        await supabase.auth.signOut()
        alert('Failed to verify account status. Please try again.')
        setIsLoading(false)
        return
      }

      const profileData = await profileResponse.json()
      const kycStatus = profileData.profile?.kycStatus

      // Redirect based on KYC status
      switch (kycStatus) {
        case 'APPROVED':
          router.push('/dashboard')
          break
        case 'PENDING_REVIEW':
          router.push('/kyc/awaiting-review')
          break
        case 'REJECTED':
          router.push('/kyc/rejected')
          break
        case 'IN_PROGRESS':
        case 'NOT_STARTED':
        default:
          router.push('/kyc/start')
          break
      }

    } catch (error) {
      console.error('Login error:', error)
      alert('An unexpected error occurred. Please try again.')
      setIsLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 flex items-center justify-center py-12 bg-gradient-to-b from-white to-green-50">
        <div className="container max-w-md px-4 md:px-6">
          <Card className="border-none shadow-lg">
            <CardHeader className="space-y-1">
              <div className="flex justify-center mb-4">
                <div className="relative flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-green-600 to-green-700 text-white">
                  <Badge className="absolute -right-2 -top-2 bg-green-100 text-green-700 text-xs">LIVE</Badge>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-6 w-6"
                  >
                    <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z" />
                    <path d="M3 9V7a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v2" />
                    <path d="M12 3v6" />
                  </svg>
                </div>
              </div>
              <CardTitle className="text-2xl text-center">Production Dashboard</CardTitle>
              <CardDescription className="text-center">
                Enter your credentials to access your live business dashboard
              </CardDescription>
            </CardHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)}>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} className="rounded-lg" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Password</FormLabel>
                        <FormControl>
                          <Input type="password" placeholder="••••••••" {...field} className="rounded-lg" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="flex items-center justify-between">
                    <FormField
                      control={form.control}
                      name="rememberMe"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-2 space-y-0">
                          <FormControl>
                            <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel className="text-sm font-normal">Remember me</FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />
                    <Link href="/forgot-password" className="text-sm text-green-600 hover:underline">
                      Forgot password?
                    </Link>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    type="submit"
                    className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 rounded-lg"
                    disabled={isLoading}
                  >
                    {isLoading ? "Logging in..." : "Access Dashboard"}
                  </Button>
                </CardFooter>
              </form>
            </Form>
          </Card>
          <div className="mt-4 text-center">
            <Link href="/login" className="text-sm text-gray-500 hover:underline">
              ← Back to login options
            </Link>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}
