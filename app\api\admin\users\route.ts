import { NextRequest, NextResponse } from 'next/server'
import { withAdminAuth } from '@/lib/api-auth'
import { prisma } from '@/lib/prisma'

/**
 * GET /api/admin/users
 * Admin-only endpoint to list all users
 */
export const GET = withAdminAuth(async (request, auth) => {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        role: true,
        studentProfile: {
          select: {
            fullName: true,
            email: true,
            kycStatus: true,
            createdAt: true
          }
        },
        adminProfile: {
          select: {
            fullName: true,
            email: true,
            createdAt: true
          }
        }
      }
    })

    const formattedUsers = users.map(user => ({
      id: user.id,
      role: user.role,
      profile: user.role === 'STUDENT' ? user.studentProfile : user.adminProfile
    }))

    return NextResponse.json({ users: formattedUsers })
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
})
