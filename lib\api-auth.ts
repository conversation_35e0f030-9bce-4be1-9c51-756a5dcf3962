import { NextRequest, NextResponse } from 'next/server'
import { UserType } from '@prisma/client'
import { getCurrentUser, AuthError } from './rbac'

/**
 * Simple API route protection utilities
 */

export interface ApiAuthResult {
  user: {
    id: string
    role: UserType
    email?: string
    fullName?: string
  }
}

/**
 * Protect API route - require authentication
 */
export async function requireAuth(request: NextRequest): Promise<ApiAuthResult> {
  const user = await getCurrentUser()
  
  if (!user) {
    throw new AuthError('Authentication required', 'UNAUTHENTICATED')
  }

  return { user }
}

/**
 * Protect API route - require admin role
 */
export async function requireAdminAuth(request: NextRequest): Promise<ApiAuthResult> {
  const { user } = await requireAuth(request)
  
  if (user.role !== UserType.ADMIN) {
    throw new AuthError('Admin access required', 'ADMIN_REQUIRED')
  }

  return { user }
}

/**
 * Protect API route - require student role
 */
export async function requireStudentAuth(request: NextRequest): Promise<ApiAuthResult> {
  const { user } = await requireAuth(request)
  
  if (user.role !== UserType.STUDENT) {
    throw new AuthError('Student access required', 'STUDENT_REQUIRED')
  }

  return { user }
}

/**
 * Protect API route - require specific role
 */
export async function requireRoleAuth(
  request: NextRequest, 
  requiredRole: UserType
): Promise<ApiAuthResult> {
  const { user } = await requireAuth(request)
  
  if (user.role !== requiredRole) {
    throw new AuthError(`${requiredRole} role required`, 'INSUFFICIENT_ROLE')
  }

  return { user }
}

/**
 * Check if user can access resource belonging to another user
 */
export async function requireResourceAccess(
  request: NextRequest,
  targetUserId: string
): Promise<ApiAuthResult> {
  const { user } = await requireAuth(request)
  
  // Admins can access any resource
  if (user.role === UserType.ADMIN) {
    return { user }
  }
  
  // Users can only access their own resources
  if (user.id !== targetUserId) {
    throw new AuthError('Access denied to this resource', 'RESOURCE_ACCESS_DENIED')
  }

  return { user }
}

/**
 * Handle API authentication errors
 */
export function handleAuthError(error: unknown): NextResponse {
  if (error instanceof AuthError) {
    const statusCode = getStatusCodeForAuthError(error.code)
    return NextResponse.json(
      { 
        error: error.message,
        code: error.code 
      },
      { status: statusCode }
    )
  }

  console.error('Unexpected error in API auth:', error)
  return NextResponse.json(
    { error: 'Internal server error' },
    { status: 500 }
  )
}

/**
 * Get appropriate HTTP status code for auth error
 */
function getStatusCodeForAuthError(code: string): number {
  switch (code) {
    case 'UNAUTHENTICATED':
      return 401
    case 'ADMIN_REQUIRED':
    case 'STUDENT_REQUIRED':
    case 'INSUFFICIENT_ROLE':
    case 'RESOURCE_ACCESS_DENIED':
      return 403
    default:
      return 400
  }
}

/**
 * Wrapper for API route handlers with authentication
 */
export function withAuth<T extends any[]>(
  handler: (request: NextRequest, auth: ApiAuthResult, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      const auth = await requireAuth(request)
      return await handler(request, auth, ...args)
    } catch (error) {
      return handleAuthError(error)
    }
  }
}

/**
 * Wrapper for API route handlers with admin authentication
 */
export function withAdminAuth<T extends any[]>(
  handler: (request: NextRequest, auth: ApiAuthResult, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      const auth = await requireAdminAuth(request)
      return await handler(request, auth, ...args)
    } catch (error) {
      return handleAuthError(error)
    }
  }
}

/**
 * Wrapper for API route handlers with student authentication
 */
export function withStudentAuth<T extends any[]>(
  handler: (request: NextRequest, auth: ApiAuthResult, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      const auth = await requireStudentAuth(request)
      return await handler(request, auth, ...args)
    } catch (error) {
      return handleAuthError(error)
    }
  }
}
