import { NextRequest, NextResponse } from 'next/server'
import { getSupabaseServerClient } from '@/lib/auth'

/**
 * Test endpoint to check Supabase connection
 */
export async function GET(request: NextRequest) {
  try {
    console.log('Testing Supabase connection...')
    
    const supabase = await getSupabaseServerClient()
    
    // Test basic connection
    const { data, error } = await supabase.auth.getUser()
    
    if (error) {
      console.log('Supabase auth error:', error)
      return NextResponse.json({
        status: 'error',
        message: 'Supabase auth error',
        error: error.message
      })
    }
    
    console.log('Supabase connection successful')
    return NextResponse.json({
      status: 'success',
      message: 'Supabase connection working',
      user: data.user ? 'User found' : 'No user logged in'
    })
    
  } catch (error) {
    console.error('Supabase connection test failed:', error)
    return NextResponse.json({
      status: 'error',
      message: 'Connection failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
