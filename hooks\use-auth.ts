'use client'

import { useEffect, useState } from 'react'
import { UserType } from '@prisma/client'
import { createClient } from '@/lib/supabase/client'

export interface AuthUser {
  id: string
  role: UserType
  email?: string
  fullName?: string
  isLoading: boolean
}

/**
 * Simple auth hook for client-side authentication and role checking
 */
export function useAuth() {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const supabase = createClient()

    // Get initial user
    const getUser = async () => {
      try {
        const { data: { user: authUser } } = await supabase.auth.getUser()
        
        if (authUser) {
          // Fetch user role from our database
          const response = await fetch('/api/auth/me')
          if (response.ok) {
            const userData = await response.json()
            setUser({
              id: authUser.id,
              role: userData.role,
              email: userData.email,
              fullName: userData.fullName,
              isLoading: false
            })
          } else {
            setUser(null)
          }
        } else {
          setUser(null)
        }
      } catch (error) {
        console.error('Error fetching user:', error)
        setUser(null)
      } finally {
        setIsLoading(false)
      }
    }

    getUser()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          // Fetch user role when signed in
          try {
            const response = await fetch('/api/auth/me')
            if (response.ok) {
              const userData = await response.json()
              setUser({
                id: session.user.id,
                role: userData.role,
                email: userData.email,
                fullName: userData.fullName,
                isLoading: false
              })
            }
          } catch (error) {
            console.error('Error fetching user data:', error)
          }
        } else if (event === 'SIGNED_OUT') {
          setUser(null)
        }
        setIsLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  // Helper functions
  const isAdmin = user?.role === UserType.ADMIN
  const isStudent = user?.role === UserType.STUDENT
  const isAuthenticated = !!user

  const hasRole = (role: UserType) => user?.role === role

  const canAccessUserResource = (targetUserId: string) => {
    if (!user) return false
    return isAdmin || user.id === targetUserId
  }

  return {
    user,
    isLoading,
    isAuthenticated,
    isAdmin,
    isStudent,
    hasRole,
    canAccessUserResource
  }
}

/**
 * Hook for role-based component rendering
 */
export function useRoleAccess() {
  const { user, isLoading } = useAuth()

  const canAccess = (allowedRoles: UserType[]) => {
    if (isLoading || !user) return false
    return allowedRoles.includes(user.role)
  }

  const adminOnly = () => canAccess([UserType.ADMIN])
  const studentOnly = () => canAccess([UserType.STUDENT])

  return {
    canAccess,
    adminOnly,
    studentOnly,
    isLoading
  }
}
