import { NextRequest, NextResponse } from 'next/server'
import { withStudentAuth } from '@/lib/api-auth'
import { prisma } from '@/lib/prisma'

/**
 * GET /api/student/profile
 * Student-only endpoint to get their own profile
 */
export const GET = withStudentAuth(async (request, auth) => {
  try {
    const profile = await prisma.studentProfile.findUnique({
      where: { userId: auth.user.id },
      select: {
        id: true,
        fullName: true,
        email: true,
        phoneNumber: true,
        kycStatus: true,
        kycCompletedAt: true,
        kycReviewedAt: true,
        createdAt: true
      }
    })

    if (!profile) {
      return NextResponse.json(
        { error: 'Profile not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ profile })
  } catch (error) {
    console.error('Error fetching student profile:', error)
    return NextResponse.json(
      { error: 'Failed to fetch profile' },
      { status: 500 }
    )
  }
})

/**
 * PUT /api/student/profile
 * Student-only endpoint to update their own profile
 */
export const PUT = withStudentAuth(async (request, auth) => {
  try {
    const body = await request.json()
    const { fullName, phoneNumber } = body

    // Validate input
    if (!fullName || typeof fullName !== 'string') {
      return NextResponse.json(
        { error: 'Full name is required' },
        { status: 400 }
      )
    }

    const updatedProfile = await prisma.studentProfile.update({
      where: { userId: auth.user.id },
      data: {
        fullName,
        phoneNumber: phoneNumber || null
      },
      select: {
        id: true,
        fullName: true,
        email: true,
        phoneNumber: true,
        kycStatus: true,
        createdAt: true
      }
    })

    return NextResponse.json({ profile: updatedProfile })
  } catch (error) {
    console.error('Error updating student profile:', error)
    return NextResponse.json(
      { error: 'Failed to update profile' },
      { status: 500 }
    )
  }
})
