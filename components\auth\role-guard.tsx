'use client'

import { ReactNode } from 'react'
import { UserType } from '@prisma/client'
import { useAuth, useRoleAccess } from '@/hooks/use-auth'

interface RoleGuardProps {
  children: ReactNode
  allowedRoles: UserType[]
  fallback?: ReactNode
  requireAuth?: boolean
}

/**
 * Component that conditionally renders children based on user role
 */
export function RoleGuard({ 
  children, 
  allowedRoles, 
  fallback = null,
  requireAuth = true 
}: RoleGuardProps) {
  const { isAuthenticated, isLoading } = useAuth()
  const { canAccess } = useRoleAccess()

  // Show loading state
  if (isLoading) {
    return <div>Loading...</div>
  }

  // Check authentication requirement
  if (requireAuth && !isAuthenticated) {
    return <>{fallback}</>
  }

  // Check role access
  if (!canAccess(allowedRoles)) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

/**
 * Admin-only component wrapper
 */
export function AdminOnly({ 
  children, 
  fallback = null 
}: { 
  children: ReactNode
  fallback?: ReactNode 
}) {
  return (
    <RoleGuard allowedRoles={[UserType.ADMIN]} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

/**
 * Student-only component wrapper
 */
export function StudentOnly({ 
  children, 
  fallback = null 
}: { 
  children: ReactNode
  fallback?: ReactNode 
}) {
  return (
    <RoleGuard allowedRoles={[UserType.STUDENT]} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

/**
 * Authenticated users only (any role)
 */
export function AuthOnly({ 
  children, 
  fallback = null 
}: { 
  children: ReactNode
  fallback?: ReactNode 
}) {
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return <div>Loading...</div>
  }

  if (!isAuthenticated) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

/**
 * Show content only for unauthenticated users
 */
export function GuestOnly({ 
  children 
}: { 
  children: ReactNode 
}) {
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return <div>Loading...</div>
  }

  if (isAuthenticated) {
    return null
  }

  return <>{children}</>
}
