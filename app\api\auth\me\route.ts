import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/rbac'

/**
 * GET /api/auth/me
 * Returns current user information including role
 */
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    return NextResponse.json({
      id: user.id,
      role: user.role,
      email: user.email,
      fullName: user.fullName
    })
  } catch (error) {
    console.error('Error in /api/auth/me:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
