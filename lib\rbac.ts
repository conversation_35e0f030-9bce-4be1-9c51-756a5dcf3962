import { UserType } from '@prisma/client'
import { getSupabaseServerClient } from './auth'
import { prisma } from './prisma'

/**
 * Simple RBAC utility functions for StarterPay
 */

export class AuthError extends Error {
  constructor(message: string, public code: string = 'AUTH_ERROR') {
    super(message)
    this.name = 'AuthError'
  }
}

export interface UserWithRole {
  id: string
  role: UserType
  email?: string
  fullName?: string
}

/**
 * Get current authenticated user with role information
 */
export async function getCurrentUser(): Promise<UserWithRole | null> {
  try {
    const supabase = await getSupabaseServerClient()
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error || !user) {
      return null
    }

    const dbUser = await prisma.user.findUnique({
      where: { id: user.id },
      select: { 
        id: true,
        role: true,
        studentProfile: {
          select: {
            fullName: true,
            email: true
          }
        },
        adminProfile: {
          select: {
            fullName: true,
            email: true
          }
        }
      }
    })

    if (!dbUser) {
      return null
    }

    const profile = dbUser.role === UserType.STUDENT 
      ? dbUser.studentProfile 
      : dbUser.adminProfile

    return {
      id: user.id,
      role: dbUser.role,
      email: profile?.email,
      fullName: profile?.fullName
    }
  } catch (error) {
    console.error('Error getting current user:', error)
    return null
  }
}

/**
 * Check if user has required role
 */
export function hasRole(userRole: UserType | undefined, requiredRole: UserType): boolean {
  return userRole === requiredRole
}

/**
 * Check if user is an admin
 */
export function isAdmin(userRole: UserType | undefined): boolean {
  return userRole === UserType.ADMIN
}

/**
 * Check if user is a student
 */
export function isStudent(userRole: UserType | undefined): boolean {
  return userRole === UserType.STUDENT
}

/**
 * Require specific role or throw error
 */
export function requireRole(userRole: UserType | undefined, requiredRole: UserType): void {
  if (!hasRole(userRole, requiredRole)) {
    throw new AuthError(`Access denied. Required role: ${requiredRole}`, 'INSUFFICIENT_ROLE')
  }
}

/**
 * Require admin role or throw error
 */
export function requireAdmin(userRole: UserType | undefined): void {
  if (!isAdmin(userRole)) {
    throw new AuthError('Access denied. Admin role required.', 'ADMIN_REQUIRED')
  }
}

/**
 * Require student role or throw error
 */
export function requireStudent(userRole: UserType | undefined): void {
  if (!isStudent(userRole)) {
    throw new AuthError('Access denied. Student role required.', 'STUDENT_REQUIRED')
  }
}

/**
 * Check if user can access another user's resources
 * Admins can access any user's resources, students can only access their own
 */
export function canAccessUserResource(
  currentUserId: string, 
  targetUserId: string, 
  currentUserRole: UserType
): boolean {
  // Admins can access any user resource
  if (isAdmin(currentUserRole)) {
    return true
  }
  
  // Users can only access their own resources
  return currentUserId === targetUserId
}

/**
 * Require access to user resource or throw error
 */
export function requireUserResourceAccess(
  currentUserId: string, 
  targetUserId: string, 
  currentUserRole: UserType
): void {
  if (!canAccessUserResource(currentUserId, targetUserId, currentUserRole)) {
    throw new AuthError('Access denied. You can only access your own resources.', 'RESOURCE_ACCESS_DENIED')
  }
}

/**
 * Simple route protection helper
 */
export function getRouteAccess(pathname: string): 'public' | 'student' | 'admin' | 'api' {
  // Public routes
  const publicRoutes = ['/', '/about', '/docs', '/sandbox-info', '/login', '/register', '/admin/login', '/admin/register']
  if (publicRoutes.some(route => pathname === route || pathname.startsWith(route + '/'))) {
    return 'public'
  }

  // Admin routes
  if (pathname.startsWith('/admin')) {
    return 'admin'
  }

  // API routes
  if (pathname.startsWith('/api')) {
    return 'api'
  }

  // Student routes (dashboard, profile, etc.)
  const studentRoutes = ['/dashboard', '/profile', '/api-keys', '/transactions', '/onboarding', '/kyc', '/sandbox']
  if (studentRoutes.some(route => pathname.startsWith(route))) {
    return 'student'
  }

  return 'public'
}

/**
 * Check if user can access a specific route
 */
export function canAccessRoute(pathname: string, userRole?: UserType): boolean {
  const routeAccess = getRouteAccess(pathname)
  
  switch (routeAccess) {
    case 'public':
      return true
    case 'student':
      return userRole === UserType.STUDENT
    case 'admin':
      return userRole === UserType.ADMIN
    case 'api':
      return userRole !== undefined // Any authenticated user can access API
    default:
      return false
  }
}
