// Datasource points to Supabase Postgres
// The POSTGRES_URL env var is provided by Vercel (and locally via .env.local)
datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_URL")
  // Only manage our own public schema; Supabase manages "auth"
  schemas  = ["public"]
  relationMode = "prisma"
}

generator client {
  provider = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

model User {
  id       String   @id @default(uuid()) @db.Uuid
  role     UserType
  studentProfile  StudentProfile?
  adminProfile    AdminProfile?

  // Sandbox relations
  sandboxWallet       SandboxWallet?
  sandboxApiKeys      SandboxApiKey[]
  sandboxTransactions SandboxTransaction[]

  @@map("users")
  @@schema("public")
}

model StudentProfile {
  id       String   @id @default(uuid()) @db.Uuid
  fullName String   @map("full_name")
  email    String   @unique
  phoneNumber String? @map("phone_number")
  createdAt DateTime @default(now()) @map("created_at")
  kycStatus   KycStatus @default(NOT_STARTED) @map("kyc_status")
  kycCompletedAt DateTime? @map("kyc_completed_at")
  kycReviewedAt DateTime? @map("kyc_reviewed_at")

  // relation back to central user
  userId  String   @unique @db.Uuid
  user    User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("student_profiles")
  @@schema("public")
}

model AdminProfile {
  id           String   @id @default(uuid()) @db.Uuid
  fullName     String   @map("full_name")
  email        String   @unique
  phoneNumber  String?
  location     String
  profession   String
  organization String?
  experience   String
  motivation   String
  availability String
  createdAt    DateTime @default(now()) @map("created_at")

  // relation back to central user
  userId String @unique @db.Uuid
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("admin_profiles")
  @@schema("public")
}

enum UserType {
  @@schema("public")
  STUDENT
  ADMIN
}

enum KycStatus {
  @@schema("public")
  NOT_STARTED
  IN_PROGRESS
  PENDING_REVIEW
  APPROVED
  REJECTED
}

// Sandbox-specific models for MTN MoMo Collections API
model SandboxWallet {
  id        String   @id @default(uuid()) @db.Uuid
  userId    String   @unique @db.Uuid
  balance   Decimal  @default(100000) @db.Decimal(15, 2) // Default 100,000 XAF
  currency  String   @default("XAF")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user         User                      @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions SandboxTransaction[]

  @@map("sandbox_wallets")
  @@schema("public")
}

model SandboxApiKey {
  id          String   @id @default(uuid()) @db.Uuid
  userId      String   @db.Uuid
  keyName     String   @map("key_name")
  keyHash     String   @map("key_hash") // Hashed version of the key
  environment String   @default("sandbox")
  isActive    Boolean  @default(true) @map("is_active")
  usageCount  Int      @default(0) @map("usage_count")
  rateLimit   Int      @default(100) @map("rate_limit") // Requests per hour
  createdAt   DateTime @default(now()) @map("created_at")
  lastUsedAt  DateTime? @map("last_used_at")

  // Relations
  user         User                      @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions SandboxTransaction[]

  @@map("sandbox_api_keys")
  @@schema("public")
}

model SandboxTransaction {
  id                    String                    @id @default(uuid()) @db.Uuid
  userId                String                    @db.Uuid
  apiKeyId              String?                   @db.Uuid @map("api_key_id")
  externalId            String                    @map("external_id") // Student's transaction ID
  amount                Decimal                   @db.Decimal(15, 2)
  currency              String                    @default("XAF")
  type                  SandboxTransactionType
  status                SandboxTransactionStatus  @default(PENDING)

  // MTN MoMo specific fields
  mtnTransactionId      String?                   @map("mtn_transaction_id") // MTN's transaction ID
  mtnReferenceId        String                    @unique @map("mtn_reference_id") // Our reference ID sent to MTN

  // Payer information
  payerPhone            String                    @map("payer_phone")
  payerMessage          String?                   @map("payer_message")
  payeeNote             String?                   @map("payee_note")

  // Metadata
  callbackUrl           String?                   @map("callback_url")
  failureReason         String?                   @map("failure_reason")

  createdAt             DateTime                  @default(now()) @map("created_at")
  updatedAt             DateTime                  @updatedAt @map("updated_at")
  completedAt           DateTime?                 @map("completed_at")

  // Relations
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  apiKey    SandboxApiKey?   @relation(fields: [apiKeyId], references: [id])
  wallet    SandboxWallet    @relation(fields: [userId], references: [userId])

  @@map("sandbox_transactions")
  @@schema("public")
}

enum SandboxTransactionType {
  @@schema("public")
  COLLECTION  // Request to Pay
}

enum SandboxTransactionStatus {
  @@schema("public")
  PENDING     // Waiting for MTN response
  SUCCESSFUL  // Payment completed
  FAILED      // Payment failed
  CANCELLED   // Payment cancelled
}
