'use server'

import { redirect } from 'next/navigation'
import { prisma } from '@/lib/prisma'
import { getSupabaseServerClient } from '@/lib/auth'

export async function login(formData: FormData) {
  const email = formData.get('email') as string
  const password = formData.get('password') as string

  const supabase = await getSupabaseServerClient()
  const { data, error } = await supabase.auth.signInWithPassword({ email, password })

  if (error) {
    return { error: error.message }
  }

  const dbUser = await prisma.user.findUnique({ where: { id: data.user.id } })
  if (dbUser?.role === 'ADMIN') {
    redirect('/admin')
  }
  redirect('/dashboard')
}

export async function signOut() {
  const supabase = await getSupabaseServerClient()
  await supabase.auth.signOut()
}
