'use server'

import { redirect } from 'next/navigation'
import { prisma } from '@/lib/prisma'
import { getSupabaseServerClient } from '@/lib/auth'
import { UserType } from '@prisma/client'

export async function studentLogin(formData: FormData) {
  const email = formData.get('email') as string
  const password = formData.get('password') as string

  const supabase = await getSupabaseServerClient()
  const { data, error } = await supabase.auth.signInWithPassword({ email, password })

  if (error) {
    return { error: error.message }
  }

  const dbUser = await prisma.user.findUnique({ where: { id: data.user.id } })

  if (!dbUser) {
    // Sign out the user if they don't exist in our database
    await supabase.auth.signOut()
    console.log(`Student login failed: User ${data.user.id} not found in database`)
    return { error: 'User account not found. Please contact support.' }
  }

  console.log(`Student login attempt: User ${data.user.id} has role ${dbUser.role}`)

  // Student login - only allow students
  if (dbUser.role !== UserType.STUDENT) {
    await supabase.auth.signOut()
    console.log(`Student login rejected: User ${data.user.id} has role ${dbUser.role}, expected STUDENT`)
    return { error: 'This login is for students only. Please use the admin login if you are an administrator.' }
  }

  console.log(`Student login successful: User ${data.user.id} redirecting to /dashboard`)
  redirect('/dashboard')
}

export async function adminLogin(formData: FormData) {
  const email = formData.get('email') as string
  const password = formData.get('password') as string

  const supabase = await getSupabaseServerClient()
  const { data, error } = await supabase.auth.signInWithPassword({ email, password })

  if (error) {
    return { error: error.message }
  }

  const dbUser = await prisma.user.findUnique({ where: { id: data.user.id } })

  if (!dbUser) {
    // Sign out the user if they don't exist in our database
    await supabase.auth.signOut()
    console.log(`Admin login failed: User ${data.user.id} not found in database`)
    return { error: 'Admin account not found. Please contact support.' }
  }

  console.log(`Admin login attempt: User ${data.user.id} has role ${dbUser.role}`)

  // Admin login - only allow admins
  if (dbUser.role !== UserType.ADMIN) {
    await supabase.auth.signOut()
    console.log(`Admin login rejected: User ${data.user.id} has role ${dbUser.role}, expected ADMIN`)
    return { error: 'This login is for administrators only. Please use the student login if you are a student.' }
  }

  console.log(`Admin login successful: User ${data.user.id} redirecting to /admin/dashboard`)
  redirect('/admin/dashboard')
}

export async function signOut() {
  const supabase = await getSupabaseServerClient()
  await supabase.auth.signOut()
}
