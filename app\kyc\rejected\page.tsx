export default function KycRejectedPage() {
  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center space-y-4 p-8 max-w-md">
        <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="32"
            height="32"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-red-600"
          >
            <line x1="18" y1="6" x2="6" y2="18" />
            <line x1="6" y1="6" x2="18" y2="18" />
          </svg>
        </div>
        <h1 className="text-2xl font-bold text-red-600">Verification Rejected</h1>
        <p className="text-gray-600">
          Your KYC verification has been rejected. Please review the feedback from our admin team and resubmit with the required corrections.
        </p>
        <div className="space-y-3 pt-4">
          <a
            href="/kyc/start"
            className="inline-block bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg w-full"
          >
            Resubmit Verification
          </a>
          <a
            href="/login/sandbox"
            className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg w-full"
          >
            Use Sandbox Instead
          </a>
          <a
            href="/login"
            className="inline-block text-gray-600 hover:text-gray-800 px-6 py-3 rounded-lg w-full border"
          >
            Back to Login
          </a>
        </div>
      </div>
    </div>
  );
}
